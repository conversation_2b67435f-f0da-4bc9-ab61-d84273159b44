import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import toast from 'react-hot-toast';
import { axiosInstance } from '../../Helpers/axiosInstance';

const initialState = {
    submissions: [],
    currentSubmission: null,
    isLoading: false,
    hasSubmitted: false
};

// Submit test answers
export const submitTest = createAsyncThunk(
    '/api/v1/test-submission/submit',
    async (testData, { rejectWithValue }) => {
        const loadingId = toast.loading('Submitting test...');
        try {
            const response = await axiosInstance.post('/api/v1/test-submission/submit', testData);
            toast.success('Test submitted successfully!', { id: loadingId });
            return response.data;
        } catch (error) {
            toast.error(error?.response?.data?.message || 'Failed to submit test', { id: loadingId });
            return rejectWithValue(error?.response?.data);
        }
    }
);

// Get test submission status
export const getTestSubmission = createAsyncThunk(
    '/api/v1/test-submission/status',
    async ({ courseId, lectureId }, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get(`/api/v1/test-submission/status/${courseId}/${lectureId}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error?.response?.data);
        }
    }
);

// Get user's test submissions
export const getUserTestSubmissions = createAsyncThunk(
    '/api/v1/test-submission/my-submissions',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get('/api/v1/test-submission/my-submissions');
            return response.data;
        } catch (error) {
            toast.error(error?.response?.data?.message || 'Failed to fetch test submissions');
            return rejectWithValue(error?.response?.data);
        }
    }
);

// Admin: Get all test submissions
export const getAllTestSubmissions = createAsyncThunk(
    '/api/v1/test-submission/admin/all',
    async (params = {}, { rejectWithValue }) => {
        try {
            const queryString = new URLSearchParams(params).toString();
            const response = await axiosInstance.get(`/api/v1/test-submission/admin/all?${queryString}`);
            return response.data;
        } catch (error) {
            toast.error(error?.response?.data?.message || 'Failed to fetch test submissions');
            return rejectWithValue(error?.response?.data);
        }
    }
);

// Admin: Get test submissions by course
export const getTestSubmissionsByCourse = createAsyncThunk(
    '/api/v1/test-submission/admin/course',
    async ({ courseId, params = {} }, { rejectWithValue }) => {
        try {
            const queryString = new URLSearchParams(params).toString();
            const response = await axiosInstance.get(`/api/v1/test-submission/admin/course/${courseId}?${queryString}`);
            return response.data;
        } catch (error) {
            toast.error(error?.response?.data?.message || 'Failed to fetch course test submissions');
            return rejectWithValue(error?.response?.data);
        }
    }
);

const testSubmissionSlice = createSlice({
    name: 'testSubmission',
    initialState,
    reducers: {
        clearCurrentSubmission: (state) => {
            state.currentSubmission = null;
            state.hasSubmitted = false;
        },
        setHasSubmitted: (state, action) => {
            state.hasSubmitted = action.payload;
        }
    },
    extraReducers: (builder) => {
        // Submit test
        builder
            .addCase(submitTest.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(submitTest.fulfilled, (state, action) => {
                state.isLoading = false;
                state.currentSubmission = action.payload.submission;
                state.hasSubmitted = true;
            })
            .addCase(submitTest.rejected, (state) => {
                state.isLoading = false;
            });

        // Get test submission status
        builder
            .addCase(getTestSubmission.fulfilled, (state, action) => {
                state.hasSubmitted = action.payload.hasSubmitted;
                state.currentSubmission = action.payload.submission;
            });

        // Get user's test submissions
        builder
            .addCase(getUserTestSubmissions.fulfilled, (state, action) => {
                state.submissions = action.payload.submissions;
            });
    }
});

export const { clearCurrentSubmission, setHasSubmitted } = testSubmissionSlice.actions;
export default testSubmissionSlice.reducer;
