import { model, Schema } from 'mongoose';

const testSubmissionSchema = new Schema({
    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    courseId: {
        type: Schema.Types.ObjectId,
        ref: 'Course',
        required: true
    },
    lectureId: {
        type: String,
        required: true
    },
    answers: [{
        questionIndex: {
            type: Number,
            required: true
        },
        selectedOption: {
            type: Number,
            required: true
        },
        isCorrect: {
            type: Boolean,
            required: true
        }
    }],
    score: {
        type: Number,
        required: true,
        min: 0,
        max: 100
    },
    totalQuestions: {
        type: Number,
        required: true
    },
    correctAnswers: {
        type: Number,
        required: true
    },
    submissionDate: {
        type: Date,
        default: Date.now
    },
    hasAttempted: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});

// Compound index to ensure a user can't submit the same test multiple times
testSubmissionSchema.index({ userId: 1, courseId: 1, lectureId: 1 }, { unique: true });

const TestSubmission = model('TestSubmission', testSubmissionSchema);

export default TestSubmission;
