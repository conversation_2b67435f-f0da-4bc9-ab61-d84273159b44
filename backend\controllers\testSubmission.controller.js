import TestSubmission from '../models/testSubmission.model.js';
import Course from '../models/course.model.js';
import CoursePurchase from '../models/coursePurchase.model.js';
import AppError from '../utils/error.utils.js';
import mongoose from 'mongoose';

// Submit test answers
export const submitTest = async (req, res, next) => {
    try {
        const { courseId, lectureId, answers } = req.body;
        const { id: userId } = req.user;

        if (!courseId || !lectureId || !answers || !Array.isArray(answers)) {
            return next(new AppError('Course ID, lecture ID, and answers are required', 400));
        }

        // Check if user has purchased the course
        const purchase = await CoursePurchase.findOne({ userId, courseId });
        if (!purchase) {
            return next(new AppError('You must purchase this course to submit tests', 403));
        }

        // Check if user has already submitted this test
        const existingSubmission = await TestSubmission.findOne({ userId, courseId, lectureId });
        if (existingSubmission) {
            return next(new AppError('You have already submitted this test. Retakes are not allowed.', 400));
        }

        // Get course and lecture details
        const course = await Course.findById(courseId);
        if (!course) {
            return next(new AppError('Course not found', 404));
        }

        // Find the specific lecture
        const lecture = course.lectures.find(lec => lec._id.toString() === lectureId);
        if (!lecture) {
            return next(new AppError('Lecture not found', 404));
        }

        if (!lecture.questions || lecture.questions.length === 0) {
            return next(new AppError('No questions found for this lecture', 404));
        }

        // Validate answers format
        if (answers.length !== lecture.questions.length) {
            return next(new AppError('Number of answers must match number of questions', 400));
        }

        // Calculate score
        let correctAnswers = 0;
        const processedAnswers = answers.map((answer, index) => {
            const question = lecture.questions[index];
            const isCorrect = answer.selectedOption === question.correctOption;
            if (isCorrect) correctAnswers++;

            return {
                questionIndex: index,
                selectedOption: answer.selectedOption,
                isCorrect
            };
        });

        const score = (correctAnswers / lecture.questions.length) * 100;

        // Create test submission
        const submission = await TestSubmission.create({
            userId,
            courseId,
            lectureId,
            answers: processedAnswers,
            score,
            totalQuestions: lecture.questions.length,
            correctAnswers
        });

        res.status(200).json({
            success: true,
            message: 'Test submitted successfully',
            submission: {
                score,
                correctAnswers,
                totalQuestions: lecture.questions.length,
                submissionDate: submission.submissionDate
            }
        });
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};

// Get user's test submission for a specific lecture
export const getTestSubmission = async (req, res, next) => {
    try {
        const { courseId, lectureId } = req.params;
        const { id: userId } = req.user;

        const submission = await TestSubmission.findOne({ userId, courseId, lectureId });

        res.status(200).json({
            success: true,
            message: 'Test submission status',
            hasSubmitted: !!submission,
            submission: submission ? {
                score: submission.score,
                correctAnswers: submission.correctAnswers,
                totalQuestions: submission.totalQuestions,
                submissionDate: submission.submissionDate
            } : null
        });
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};

// Get all test submissions for a user
export const getUserTestSubmissions = async (req, res, next) => {
    try {
        const { id: userId } = req.user;

        const submissions = await TestSubmission.find({ userId })
            .populate('courseId', 'title')
            .sort({ submissionDate: -1 });

        res.status(200).json({
            success: true,
            message: 'User test submissions fetched successfully',
            submissions
        });
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};

// Admin: Get all test submissions
export const getAllTestSubmissions = async (req, res, next) => {
    try {
        const { page = 1, limit = 10, courseId, userId } = req.query;

        const filter = {};
        if (courseId) filter.courseId = courseId;
        if (userId) filter.userId = userId;

        const submissions = await TestSubmission.find(filter)
            .populate('userId', 'fullName email')
            .populate('courseId', 'title')
            .sort({ submissionDate: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await TestSubmission.countDocuments(filter);

        res.status(200).json({
            success: true,
            message: 'All test submissions fetched successfully',
            submissions,
            totalPages: Math.ceil(total / limit),
            currentPage: page,
            total
        });
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};

// Admin: Get test submissions by course
export const getTestSubmissionsByCourse = async (req, res, next) => {
    try {
        const { courseId } = req.params;
        const { page = 1, limit = 10 } = req.query;

        const submissions = await TestSubmission.find({ courseId })
            .populate('userId', 'fullName email')
            .sort({ submissionDate: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await TestSubmission.countDocuments({ courseId });

        // Calculate statistics
        const stats = await TestSubmission.aggregate([
            { $match: { courseId: new mongoose.Types.ObjectId(courseId) } },
            {
                $group: {
                    _id: null,
                    averageScore: { $avg: '$score' },
                    highestScore: { $max: '$score' },
                    lowestScore: { $min: '$score' },
                    totalSubmissions: { $sum: 1 }
                }
            }
        ]);

        res.status(200).json({
            success: true,
            message: 'Course test submissions fetched successfully',
            submissions,
            stats: stats[0] || {
                averageScore: 0,
                highestScore: 0,
                lowestScore: 0,
                totalSubmissions: 0
            },
            totalPages: Math.ceil(total / limit),
            currentPage: page,
            total
        });
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};
