import {configureStore} from "@reduxjs/toolkit"
import AuthSliceReducer from "./Slices/AuthSlice"
import CourseSliceReducer from "./Slices/CourseSlice"
import RazorpaySliceReducer from "./Slices/RazorpaySlice"
import LectureSliceReducer from "./Slices/LectureSlice"
import StatSliceReducer from "./Slices/StatSlice"
import BlogReducer from './Slices/BlogSlice';
import CoursePurchaseReducer from './Slices/CoursePurchaseSlice';
import TestSubmissionReducer from './Slices/TestSubmissionSlice';


 const store = configureStore({
    reducer: {
        auth: AuthSliceReducer,
        course: CourseSliceReducer,
        razorpay: RazorpaySliceReducer,
        lecture: LectureSliceReducer,
        stat: StatSliceReducer,
        blogs: BlogReducer,
        coursePurchase: CoursePurchaseReducer,
        testSubmission: TestSubmissionReducer
    },
    devTools: true
})

export default store