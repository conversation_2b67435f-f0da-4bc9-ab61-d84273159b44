import { model, Schema } from 'mongoose';

const coursePurchaseSchema = new Schema({
    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    courseId: {
        type: Schema.Types.ObjectId,
        ref: 'Course',
        required: true
    },
    purchaseDate: {
        type: Date,
        default: Date.now
    },
    amount: {
        type: Number,
        required: true
    },
    paymentId: {
        type: String,
        required: true
    },
    paymentStatus: {
        type: String,
        enum: ['pending', 'completed', 'failed'],
        default: 'completed'
    }
}, {
    timestamps: true
});

// Compound index to ensure a user can't purchase the same course multiple times
coursePurchaseSchema.index({ userId: 1, courseId: 1 }, { unique: true });

const CoursePurchase = model('CoursePurchase', coursePurchaseSchema);

export default CoursePurchase;
