import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Layout from '../../Layout/Layout';
import { getAllTestSubmissions, getTestSubmissionsByCourse } from '../../Redux/Slices/TestSubmissionSlice';
import { getAllCourses } from '../../Redux/Slices/CourseSlice';
import { FaSearch, FaDownload, FaEye } from 'react-icons/fa';
import toast from 'react-hot-toast';

export default function TestScoresDashboard() {
  const dispatch = useDispatch();
  const { coursesData } = useSelector((state) => state.course);
  
  const [submissions, setSubmissions] = useState([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [filters, setFilters] = useState({
    courseId: '',
    userId: '',
    page: 1,
    limit: 10
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState(null);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    dispatch(getAllCourses());
    fetchTestSubmissions();
  }, []);

  useEffect(() => {
    fetchTestSubmissions();
  }, [filters]);

  useEffect(() => {
    filterSubmissions();
  }, [searchTerm, submissions]);

  const fetchTestSubmissions = async () => {
    try {
      setIsLoading(true);
      let result;
      
      if (filters.courseId) {
        result = await dispatch(getTestSubmissionsByCourse({
          courseId: filters.courseId,
          params: {
            page: filters.page,
            limit: filters.limit
          }
        })).unwrap();
        setStats(result.stats);
      } else {
        result = await dispatch(getAllTestSubmissions({
          page: filters.page,
          limit: filters.limit,
          userId: filters.userId
        })).unwrap();
      }
      
      setSubmissions(result.submissions || []);
      setTotalPages(result.totalPages || 1);
    } catch (error) {
      toast.error('Failed to fetch test submissions');
    } finally {
      setIsLoading(false);
    }
  };

  const filterSubmissions = () => {
    if (!searchTerm) {
      setFilteredSubmissions(submissions);
      return;
    }

    const filtered = submissions.filter(submission => 
      submission.userId?.fullName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.userId?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.courseId?.title?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    setFilteredSubmissions(filtered);
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  const handlePageChange = (newPage) => {
    setFilters(prev => ({
      ...prev,
      page: newPage
    }));
  };

  const exportToCSV = () => {
    const csvData = filteredSubmissions.map(submission => ({
      'Student Name': submission.userId?.fullName || 'N/A',
      'Student Email': submission.userId?.email || 'N/A',
      'Course': submission.courseId?.title || 'N/A',
      'Score': `${submission.score}%`,
      'Correct Answers': `${submission.correctAnswers}/${submission.totalQuestions}`,
      'Submission Date': new Date(submission.submissionDate).toLocaleDateString()
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'test-scores.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4 md:mb-0">
                Test Scores Dashboard
              </h1>
              <button
                onClick={exportToCSV}
                disabled={filteredSubmissions.length === 0}
                className="flex items-center space-x-2 bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <FaDownload />
                <span>Export CSV</span>
              </button>
            </div>

            {/* Statistics */}
            {stats && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-100 dark:bg-blue-900 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-blue-600 dark:text-blue-300">Total Submissions</h3>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{stats.totalSubmissions}</p>
                </div>
                <div className="bg-green-100 dark:bg-green-900 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-green-600 dark:text-green-300">Average Score</h3>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">{stats.averageScore?.toFixed(1)}%</p>
                </div>
                <div className="bg-yellow-100 dark:bg-yellow-900 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-yellow-600 dark:text-yellow-300">Highest Score</h3>
                  <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">{stats.highestScore}%</p>
                </div>
                <div className="bg-red-100 dark:bg-red-900 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-red-600 dark:text-red-300">Lowest Score</h3>
                  <p className="text-2xl font-bold text-red-900 dark:text-red-100">{stats.lowestScore}%</p>
                </div>
              </div>
            )}

            {/* Filters */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Filter by Course
                </label>
                <select
                  value={filters.courseId}
                  onChange={(e) => handleFilterChange('courseId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="">All Courses</option>
                  {coursesData.map(course => (
                    <option key={course._id} value={course._id}>
                      {course.title}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Search Students
                </label>
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by name or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Items per page
                </label>
                <select
                  value={filters.limit}
                  onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value={10}>10</option>
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
              </div>
            </div>

            {/* Table */}
            <div className="overflow-x-auto">
              {isLoading ? (
                <div className="text-center py-8">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  <p className="mt-2 text-gray-600 dark:text-gray-400">Loading test submissions...</p>
                </div>
              ) : filteredSubmissions.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-600 dark:text-gray-400">No test submissions found.</p>
                </div>
              ) : (
                <table className="min-w-full bg-white dark:bg-gray-800">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Student
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Course
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Score
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Correct Answers
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Submission Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredSubmissions.map((submission) => (
                      <tr key={submission._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {submission.userId?.fullName || 'N/A'}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {submission.userId?.email || 'N/A'}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">
                            {submission.courseId?.title || 'N/A'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            submission.score >= 80 
                              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                              : submission.score >= 60
                              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                          }`}>
                            {submission.score}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {submission.correctAnswers}/{submission.totalQuestions}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {new Date(submission.submissionDate).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  Page {filters.page} of {totalPages}
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handlePageChange(filters.page - 1)}
                    disabled={filters.page === 1}
                    className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handlePageChange(filters.page + 1)}
                    disabled={filters.page === totalPages}
                    className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
}
