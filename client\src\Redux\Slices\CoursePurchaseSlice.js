import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import toast from 'react-hot-toast';
import { axiosInstance } from '../../Helpers/axiosInstance';

const initialState = {
    purchasedCourses: [],
    isLoading: false,
    purchaseStatus: null
};

// Create course order
export const createCourseOrder = createAsyncThunk(
    '/api/v1/course-purchase/order',
    async (courseId, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post('/api/v1/course-purchase/order', { courseId });
            return response.data;
        } catch (error) {
            toast.error(error?.response?.data?.message || 'Failed to create order');
            return rejectWithValue(error?.response?.data);
        }
    }
);

// Verify course payment
export const verifyCoursePayment = createAsyncThunk(
    '/api/v1/course-purchase/verify',
    async (paymentData, { rejectWithValue }) => {
        const loadingId = toast.loading('Verifying payment...');
        try {
            const response = await axiosInstance.post('/api/v1/course-purchase/verify', paymentData);
            toast.success('Course purchased successfully!', { id: loadingId });
            return response.data;
        } catch (error) {
            toast.error(error?.response?.data?.message || 'Payment verification failed', { id: loadingId });
            return rejectWithValue(error?.response?.data);
        }
    }
);

// Get user's purchased courses
export const getUserPurchasedCourses = createAsyncThunk(
    '/api/v1/course-purchase/my-courses',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get('/api/v1/course-purchase/my-courses');
            return response.data;
        } catch (error) {
            toast.error(error?.response?.data?.message || 'Failed to fetch purchased courses');
            return rejectWithValue(error?.response?.data);
        }
    }
);

// Check if user has purchased a specific course
export const checkCoursePurchase = createAsyncThunk(
    '/api/v1/course-purchase/check',
    async (courseId, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get(`/api/v1/course-purchase/check/${courseId}`);
            return { courseId, ...response.data };
        } catch (error) {
            return rejectWithValue(error?.response?.data);
        }
    }
);

// Admin: Get all course purchases
export const getAllCoursePurchases = createAsyncThunk(
    '/api/v1/course-purchase/admin/all',
    async (params = {}, { rejectWithValue }) => {
        try {
            const queryString = new URLSearchParams(params).toString();
            const response = await axiosInstance.get(`/api/v1/course-purchase/admin/all?${queryString}`);
            return response.data;
        } catch (error) {
            toast.error(error?.response?.data?.message || 'Failed to fetch course purchases');
            return rejectWithValue(error?.response?.data);
        }
    }
);

const coursePurchaseSlice = createSlice({
    name: 'coursePurchase',
    initialState,
    reducers: {
        clearPurchaseStatus: (state) => {
            state.purchaseStatus = null;
        }
    },
    extraReducers: (builder) => {
        // Create course order
        builder
            .addCase(createCourseOrder.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(createCourseOrder.fulfilled, (state, action) => {
                state.isLoading = false;
                state.purchaseStatus = 'order_created';
            })
            .addCase(createCourseOrder.rejected, (state) => {
                state.isLoading = false;
                state.purchaseStatus = 'order_failed';
            });

        // Verify course payment
        builder
            .addCase(verifyCoursePayment.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(verifyCoursePayment.fulfilled, (state, action) => {
                state.isLoading = false;
                state.purchaseStatus = 'payment_verified';
            })
            .addCase(verifyCoursePayment.rejected, (state) => {
                state.isLoading = false;
                state.purchaseStatus = 'payment_failed';
            });

        // Get user's purchased courses
        builder
            .addCase(getUserPurchasedCourses.fulfilled, (state, action) => {
                state.purchasedCourses = action.payload.purchases;
            });

        // Check course purchase
        builder
            .addCase(checkCoursePurchase.fulfilled, (state, action) => {
                // Store purchase status for specific course
                const { courseId, hasPurchased } = action.payload;
                state.purchaseStatus = hasPurchased ? 'purchased' : 'not_purchased';
            });
    }
});

export const { clearPurchaseStatus } = coursePurchaseSlice.actions;
export default coursePurchaseSlice.reducer;
