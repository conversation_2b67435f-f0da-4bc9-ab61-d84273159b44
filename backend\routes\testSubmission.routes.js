import { Router } from 'express';
import {
    submitTest,
    getTestSubmission,
    getUserTestSubmissions,
    getAllTestSubmissions,
    getTestSubmissionsByCourse
} from '../controllers/testSubmission.controller.js';
import { isLoggedIn, authorisedRoles } from '../middleware/auth.middleware.js';
import { checkTestAccess } from '../middleware/courseAccess.middleware.js';

const router = Router();

// User routes
router.route('/submit')
    .post(isLoggedIn, checkTestAccess, submitTest);

router.route('/status/:courseId/:lectureId')
    .get(isLoggedIn, getTestSubmission);

router.route('/my-submissions')
    .get(isLoggedIn, getUserTestSubmissions);

// Admin routes
router.route('/admin/all')
    .get(isLoggedIn, authorisedRoles('ADMIN'), getAllTestSubmissions);

router.route('/admin/course/:courseId')
    .get(isLoggedIn, authorisedRoles('ADMIN'), getTestSubmissionsByCourse);

export default router;
