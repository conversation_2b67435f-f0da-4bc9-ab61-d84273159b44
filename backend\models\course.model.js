import { model, Schema } from "mongoose";

const courseSchema = new Schema({
    title: {
        type: String,
        unique: true,
        required: [true, 'Title is required'],
        minLength: [8, 'Title must be at least 8 character'],
        maxLength: [59, 'Title should be less than 60 character'],
        trim: true
    },
    description: {
        type: String,
        required: true,
        minLength: [8, 'Description must be at least 8 character'],
        maxLength: [500, 'Description should be less than 500 character'],
    },
    category: {
        type: String,
        required: [true, 'Category is required'],
    },
    thumbnail: {
        public_id: {
            type: String
        },
        secure_url: {
            type: String
        }
    },
    lectures: [
        {
            title: String,
            description: String,
            link: String,
            videoURL: {
                type: String,
                trim: true,
                validate: {
                    validator: function(v) {
                        if (!v) return true; // Allow empty values
                        // Validate YouTube embed URL format
                        const youtubeEmbedRegex = /^https:\/\/www\.youtube\.com\/embed\/[a-zA-Z0-9_-]+$/;
                        return youtubeEmbedRegex.test(v);
                    },
                    message: 'Please provide a valid YouTube embed URL (https://www.youtube.com/embed/VIDEO_ID)'
                }
            },
            lecture: {
                public_id: {
                    type: String
                },
                secure_url: {
                    type: String
                }
            },
            materials: {
                public_id: {
                    type: String
                },
                secure_url: {
                    type: String
                }
            },
            questions: [
                {
                    questionText: {
                        type: String,
                    },
                    options: [{
                        type: String,
                    }],
                    correctOption: {
                        type: Number,
                    },
                }
            ]
        }

    ],
    numberOfLectures: {
        type: Number,
        default: 0
    },
    createdBy: {
        type: String,
        required: true,
    },
    price: {
        type: Number,
        required: [true, 'Price is required'],
        min: [0, 'Price cannot be negative']
    },
    videoURL: {
        type: String,
        trim: true,
        validate: {
            validator: function(v) {
                if (!v) return true; // Allow empty values
                // Validate YouTube embed URL format
                const youtubeEmbedRegex = /^https:\/\/www\.youtube\.com\/embed\/[a-zA-Z0-9_-]+$/;
                return youtubeEmbedRegex.test(v);
            },
            message: 'Please provide a valid YouTube embed URL (https://www.youtube.com/embed/VIDEO_ID)'
        }
    },
    videoFile: {
        public_id: {
            type: String
        },
        secure_url: {
            type: String
        }
    }
},
    {
        timestamps: true
    })

const Course = model("Course", courseSchema);

export default Course