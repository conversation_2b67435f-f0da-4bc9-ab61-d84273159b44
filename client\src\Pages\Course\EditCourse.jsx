import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { updateCourse, getAllCourses } from "../../Redux/Slices/CourseSlice";
import { useNavigate, useLocation } from "react-router-dom";
import Layout from "../../Layout/Layout";
import toast from "react-hot-toast";
import InputBox from "../../Components/InputBox/InputBox";
import TextArea from "../../Components/InputBox/TextArea";

export default function EditCourse() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const courseData = location.state;

  const [isUpdatingCourse, setIsUpdatingCourse] = useState(false);
  const [userInput, setUserInput] = useState({
    title: "",
    category: "",
    createdBy: "",
    description: "",
    price: "",
    thumbnail: null,
    previewImage: "",
    videoURL: "",
    videoFile: null,
    previewVideo: "",
  });

  useEffect(() => {
    if (courseData) {
      setUserInput({
        title: courseData.title || "",
        category: courseData.category || "",
        createdBy: courseData.createdBy || "",
        description: courseData.description || "",
        price: courseData.price || "",
        thumbnail: null,
        previewImage: courseData.thumbnail?.secure_url || "",
        videoURL: courseData.videoURL || "",
        videoFile: null,
        previewVideo: courseData.videoFile?.secure_url || "",
      });
    } else {
      navigate("/admin/dashboard");
    }
  }, [courseData, navigate]);

  function handleImageUpload(e) {
    e.preventDefault();
    const uploadImage = e.target.files[0];
    if (uploadImage) {
      const fileReader = new FileReader();
      fileReader.readAsDataURL(uploadImage);
      fileReader.addEventListener("load", function () {
        setUserInput({
          ...userInput,
          previewImage: this.result,
          thumbnail: uploadImage,
        });
      });
    }
  }

  function handleVideoUpload(e) {
    e.preventDefault();
    const uploadVideo = e.target.files[0];
    if (uploadVideo) {
      const fileReader = new FileReader();
      fileReader.readAsDataURL(uploadVideo);
      fileReader.addEventListener("load", function () {
        setUserInput({
          ...userInput,
          previewVideo: this.result,
          videoFile: uploadVideo,
          videoURL: "", // Clear YouTube URL when video file is selected
        });
      });
    }
  }

  function handleUserInput(e) {
    const { name, value } = e.target;
    const updatedInput = {
      ...userInput,
      [name]: value,
    };
    
    // Clear video file when YouTube URL is entered
    if (name === "videoURL" && value.trim()) {
      updatedInput.videoFile = null;
      updatedInput.previewVideo = "";
    }
    
    setUserInput(updatedInput);
  }

  async function onFormSubmit(e) {
    e.preventDefault();

    if (
      !userInput.title ||
      !userInput.description ||
      !userInput.category ||
      !userInput.createdBy ||
      !userInput.price
    ) {
      toast.error("All fields including price are required!");
      return;
    }

    if (isNaN(userInput.price) || parseFloat(userInput.price) < 0) {
      toast.error("Please enter a valid price!");
      return;
    }

    setIsUpdatingCourse(true);
    const formData = new FormData();
    formData.append("title", userInput.title);
    formData.append("description", userInput.description);
    formData.append("category", userInput.category);
    formData.append("createdBy", userInput.createdBy);
    formData.append("price", userInput.price);
    
    // Only append thumbnail if a new one is selected
    if (userInput.thumbnail) {
      formData.append("thumbnail", userInput.thumbnail);
    }
    
    // Add video data
    if (userInput.videoURL) {
      formData.append("videoURL", userInput.videoURL);
    }
    if (userInput.videoFile) {
      formData.append("videoFile", userInput.videoFile);
    }

    const response = await dispatch(updateCourse({ 
      id: courseData._id, 
      formData 
    }));
    
    if (response?.payload?.success) {
      await dispatch(getAllCourses());
      navigate("/admin/dashboard");
    }
    setIsUpdatingCourse(false);
  }

  return (
    <Layout>
      <section className="flex flex-col gap-6 items-center py-8 px-3 min-h-[100vh]">
        <form
          onSubmit={onFormSubmit}
          autoComplete="off"
          noValidate
          className="flex flex-col dark:bg-base-100 gap-7 rounded-lg md:py-5 py-7 md:px-7 px-3 md:w-[750px] w-full shadow-custom dark:shadow-xl  "
        >
          <h1 className="text-center dark:text-purple-500 text-4xl font-bold font-inter">
            Edit Course
          </h1>
          <div className="w-full flex md:flex-row md:justify-between justify-center flex-col md:gap-0 gap-5">
            <div className="md:w-[48%] w-full flex flex-col gap-5">
              {/* thumbnail */}
              <div className="border border-gray-300">
                <label htmlFor="image_uploads" className="cursor-pointer">
                  {userInput.previewImage ? (
                    <img
                      className="w-full h-44 m-auto"
                      src={userInput.previewImage}
                      alt="Course thumbnail"
                    />
                  ) : (
                    <div className="w-full h-44 m-auto flex items-center justify-center bg-gray-100 dark:bg-gray-800">
                      <span className="text-gray-500">Upload Thumbnail</span>
                    </div>
                  )}
                </label>
                <input
                  className="hidden"
                  type="file"
                  id="image_uploads"
                  accept=".jpg, .jpeg, .png"
                  name="image_uploads"
                  onChange={handleImageUpload}
                />
              </div>
              {/* title */}
              <InputBox
                label={"Title"}
                name={"title"}
                type={"text"}
                placeholder={"Enter Course Title"}
                onChange={handleUserInput}
                value={userInput.title}
              />
            </div>
            <div className="md:w-[48%] w-full flex flex-col gap-5">
              {/* instructor */}
              <InputBox
                label={"Instructor"}
                name={"createdBy"}
                type={"text"}
                placeholder={"Enter Course instructor"}
                onChange={handleUserInput}
                value={userInput.createdBy}
              />
              {/* category */}
              <InputBox
                label={"Category"}
                name={"category"}
                type={"text"}
                placeholder={"Enter Course Category"}
                onChange={handleUserInput}
                value={userInput.category}
              />
              {/* price */}
              <InputBox
                label={"Price (₹)"}
                name={"price"}
                type={"number"}
                placeholder={"Enter Course Price"}
                onChange={handleUserInput}
                value={userInput.price}
                min="0"
                step="0.01"
              />
              {/* description */}
              <TextArea
                label={"Description"}
                name={"description"}
                rows={3}
                type={"text"}
                placeholder={"Enter Course Description"}
                onChange={handleUserInput}
                value={userInput.description}
              />
            </div>
          </div>

          {/* Video Section */}
          <div className="w-full">
            <h3 className="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">
              Course Video (Optional)
            </h3>
            <div className="flex flex-col gap-4">
              {/* YouTube URL */}
              <InputBox
                label={"YouTube Video URL"}
                name={"videoURL"}
                type={"url"}
                placeholder={"https://www.youtube.com/watch?v=VIDEO_ID or https://youtu.be/VIDEO_ID"}
                onChange={handleUserInput}
                value={userInput.videoURL}
              />
              
              <div className="text-center text-gray-500 dark:text-gray-400 font-medium">
                OR
              </div>
              
              {/* Video File Upload */}
              <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-4">
                <label htmlFor="video_upload" className="cursor-pointer">
                  <div className="text-center">
                    {userInput.previewVideo ? (
                      <video
                        className="w-full h-48 mx-auto rounded"
                        src={userInput.previewVideo}
                        controls
                      />
                    ) : (
                      <div className="flex flex-col items-center justify-center h-32 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded">
                        <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                          Click to upload video file
                        </p>
                        <p className="text-xs text-gray-400">
                          MP4 files only (Max: 500MB)
                        </p>
                      </div>
                    )}
                  </div>
                </label>
                <input
                  className="hidden"
                  type="file"
                  id="video_upload"
                  accept=".mp4"
                  name="video_upload"
                  onChange={handleVideoUpload}
                />
              </div>
              
              <p className="text-sm text-gray-500 dark:text-gray-400">
                💡 <strong>Tip:</strong> YouTube URL takes priority over uploaded video file. 
                If both are provided, only the YouTube video will be used.
              </p>
            </div>
          </div>

          {/* submit btn */}
          <button
            type="submit"
            disabled={isUpdatingCourse}
            className="mt-3 bg-yellow-500 text-white dark:text-base-200  transition-all ease-in-out duration-300 rounded-md py-2 font-nunito-sans font-[500]  text-lg cursor-pointer"
          >
            {isUpdatingCourse ? "Updating Course..." : "Update Course"}
          </button>
        </form>
      </section>
    </Layout>
  );
}
