Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFA7590000 ntdll.dll
7FFFA59F0000 KERNEL32.DLL
7FFFA4E40000 KERNELBASE.dll
7FFFA5690000 USER32.dll
7FFFA4910000 win32u.dll
7FFFA5910000 GDI32.dll
7FFFA4D10000 gdi32full.dll
7FFFA4940000 msvcp_win.dll
7FFFA47F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFA5850000 advapi32.dll
7FFFA6460000 msvcrt.dll
7FFFA7170000 sechost.dll
7FFFA49E0000 bcrypt.dll
7FFFA6DD0000 RPCRT4.dll
7FFFA3CE0000 CRYPTBASE.DLL
7FFFA4C90000 bcryptPrimitives.dll
7FFFA7130000 IMM32.DLL
