import CoursePurchase from '../models/coursePurchase.model.js';
import Course from '../models/course.model.js';
import User from '../models/user.model.js';
import AppError from '../utils/error.utils.js';
import crypto from 'crypto';
import { razorpay } from '../server.js';

// Create course purchase order
export const createCourseOrder = async (req, res, next) => {
    try {
        const { courseId } = req.body;
        const { id: userId } = req.user;

        if (!courseId) {
            return next(new AppError('Course ID is required', 400));
        }

        // Validate Razorpay configuration
        if (!razorpay) {
            console.error('Razorpay instance not available');
            return next(new AppError('Payment service not configured. Please contact administrator.', 500));
        }

        if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_SECRET) {
            console.error('Razorpay credentials missing:', {
                keyId: !!process.env.RAZORPAY_KEY_ID,
                secret: !!process.env.RAZORPAY_SECRET
            });
            return next(new AppError('Payment service not configured. Please contact administrator.', 500));
        }

        // Check if course exists
        const course = await Course.findById(courseId);
        if (!course) {
            return next(new AppError('Course not found', 404));
        }

        // Check if user already purchased this course
        const existingPurchase = await CoursePurchase.findOne({ userId, courseId });
        if (existingPurchase) {
            return next(new AppError('You have already purchased this course', 400));
        }

        // Create Razorpay order
        // Generate a shorter receipt (max 40 chars for Razorpay)
        const timestamp = Date.now().toString().slice(-8); // Last 8 digits
        const shortCourseId = courseId.slice(-8); // Last 8 chars of course ID
        const shortUserId = userId.slice(-8); // Last 8 chars of user ID
        const receipt = `c${shortCourseId}u${shortUserId}t${timestamp}`; // 27 chars max

        const options = {
            amount: course.price * 100, // amount in paise
            currency: 'INR',
            receipt: receipt,
            notes: {
                courseId,
                userId,
                courseName: course.title
            }
        };

        console.log('Creating Razorpay order with options:', options);
        console.log('Receipt length:', receipt.length, 'Receipt:', receipt);
        const order = await razorpay.orders.create(options);
        console.log('Razorpay order created successfully:', order.id);

        res.status(200).json({
            success: true,
            message: 'Course order created successfully',
            order,
            course: {
                id: course._id,
                title: course.title,
                price: course.price
            }
        });
    } catch (error) {
        console.error('Error creating course order:', error);
        return next(new AppError(error.message || 'Failed to create course order', 500));
    }
};

// Verify course purchase payment
export const verifyCoursePayment = async (req, res, next) => {
    try {
        const { razorpay_order_id, razorpay_payment_id, razorpay_signature, courseId } = req.body;
        const { id: userId } = req.user;

        if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature || !courseId) {
            return next(new AppError('All payment details are required', 400));
        }

        // Verify signature
        const body = razorpay_order_id + "|" + razorpay_payment_id;
        const expectedSignature = crypto
            .createHmac("sha256", process.env.RAZORPAY_SECRET)
            .update(body.toString())
            .digest("hex");

        if (expectedSignature !== razorpay_signature) {
            return next(new AppError('Payment verification failed', 400));
        }

        // Get course details
        const course = await Course.findById(courseId);
        if (!course) {
            return next(new AppError('Course not found', 404));
        }

        // Check if purchase already exists
        const existingPurchase = await CoursePurchase.findOne({ userId, courseId });
        if (existingPurchase) {
            return next(new AppError('Course already purchased', 400));
        }

        // Create purchase record
        const purchase = await CoursePurchase.create({
            userId,
            courseId,
            amount: course.price,
            paymentId: razorpay_payment_id,
            paymentStatus: 'completed'
        });

        res.status(200).json({
            success: true,
            message: 'Course purchased successfully',
            purchase
        });
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};

// Get user's purchased courses
export const getUserPurchasedCourses = async (req, res, next) => {
    try {
        const { id: userId } = req.user;

        const purchases = await CoursePurchase.find({ userId })
            .populate('courseId', 'title description thumbnail category numberOfLectures createdBy price')
            .sort({ purchaseDate: -1 });

        res.status(200).json({
            success: true,
            message: 'Purchased courses fetched successfully',
            purchases
        });
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};

// Check if user has purchased a specific course
export const checkCoursePurchase = async (req, res, next) => {
    try {
        const { courseId } = req.params;
        const { id: userId } = req.user;

        const purchase = await CoursePurchase.findOne({ userId, courseId });

        res.status(200).json({
            success: true,
            message: 'Course purchase status',
            hasPurchased: !!purchase,
            purchase
        });
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};

// Admin: Get all course purchases
export const getAllCoursePurchases = async (req, res, next) => {
    try {
        const { page = 1, limit = 10, courseId, userId } = req.query;

        const filter = {};
        if (courseId) filter.courseId = courseId;
        if (userId) filter.userId = userId;

        const purchases = await CoursePurchase.find(filter)
            .populate('userId', 'fullName email')
            .populate('courseId', 'title price')
            .sort({ purchaseDate: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await CoursePurchase.countDocuments(filter);

        res.status(200).json({
            success: true,
            message: 'All course purchases fetched successfully',
            purchases,
            totalPages: Math.ceil(total / limit),
            currentPage: page,
            total
        });
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};
