import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Layout from '../../Layout/Layout';
import { getUserPurchasedCourses } from '../../Redux/Slices/CoursePurchaseSlice';
import { getUserTestSubmissions } from '../../Redux/Slices/TestSubmissionSlice';
import { FaPlay, FaBook, FaUser, FaCalendar, FaRupeeSign, FaTrophy } from 'react-icons/fa';
import toast from 'react-hot-toast';

export default function PurchasedCourses() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { purchasedCourses, isLoading } = useSelector((state) => state.coursePurchase);
  const { submissions } = useSelector((state) => state.testSubmission);
  
  const [testScores, setTestScores] = useState({});

  useEffect(() => {
    fetchPurchasedCourses();
    fetchTestSubmissions();
  }, []);

  useEffect(() => {
    // Process test submissions to get scores by course
    if (submissions.length > 0) {
      const scoresByCourse = {};
      submissions.forEach(submission => {
        const courseId = submission.courseId._id || submission.courseId;
        if (!scoresByCourse[courseId]) {
          scoresByCourse[courseId] = [];
        }
        scoresByCourse[courseId].push({
          score: submission.score,
          submissionDate: submission.submissionDate
        });
      });
      setTestScores(scoresByCourse);
    }
  }, [submissions]);

  const fetchPurchasedCourses = async () => {
    try {
      await dispatch(getUserPurchasedCourses()).unwrap();
    } catch (error) {
      toast.error('Failed to fetch purchased courses');
    }
  };

  const fetchTestSubmissions = async () => {
    try {
      await dispatch(getUserTestSubmissions()).unwrap();
    } catch (error) {
      console.error('Failed to fetch test submissions:', error);
    }
  };

  const handleStartCourse = (course) => {
    navigate('/course/displaylectures', { state: course });
  };

  const getAverageScore = (courseId) => {
    const scores = testScores[courseId];
    if (!scores || scores.length === 0) return null;
    
    const average = scores.reduce((sum, test) => sum + test.score, 0) / scores.length;
    return average.toFixed(1);
  };

  const getTestCount = (courseId) => {
    const scores = testScores[courseId];
    return scores ? scores.length : 0;
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">Loading your courses...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
              My Purchased Courses
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Access your purchased courses and track your progress
            </p>
          </div>

          {purchasedCourses.length === 0 ? (
            <div className="text-center py-12">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                <FaBook className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  No Courses Purchased Yet
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Start your learning journey by purchasing your first course
                </p>
                <button
                  onClick={() => navigate('/courses')}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                >
                  Browse Courses
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {purchasedCourses.map((purchase) => {
                const course = purchase.courseId;
                const averageScore = getAverageScore(course._id);
                const testCount = getTestCount(course._id);
                
                return (
                  <div
                    key={purchase._id}
                    className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
                  >
                    <div className="relative">
                      <img
                        src={course.thumbnail?.secure_url}
                        alt={course.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                        Purchased
                      </div>
                    </div>
                    
                    <div className="p-6">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                        {course.title}
                      </h3>
                      
                      <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                        {course.description}
                      </p>
                      
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <FaUser className="mr-2 text-blue-500" />
                          <span>Instructor: {course.createdBy}</span>
                        </div>
                        
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <FaBook className="mr-2 text-green-500" />
                          <span>Lectures: {course.numberOfLectures}</span>
                        </div>
                        
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <FaRupeeSign className="mr-2 text-yellow-500" />
                          <span>Paid: ₹{purchase.amount}</span>
                        </div>
                        
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <FaCalendar className="mr-2 text-purple-500" />
                          <span>Purchased: {new Date(purchase.purchaseDate).toLocaleDateString()}</span>
                        </div>
                      </div>

                      {/* Test Progress */}
                      {testCount > 0 && (
                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <FaTrophy className="mr-2 text-yellow-500" />
                              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                Test Progress
                              </span>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-semibold text-gray-900 dark:text-white">
                                Avg: {averageScore}%
                              </div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">
                                {testCount} test{testCount > 1 ? 's' : ''} completed
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      <button
                        onClick={() => handleStartCourse(course)}
                        className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold transition-colors flex items-center justify-center space-x-2"
                      >
                        <FaPlay className="text-sm" />
                        <span>Continue Learning</span>
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
