import { Router } from 'express';
import {
    createCourseOrder,
    verifyCoursePayment,
    getUserPurchasedCourses,
    checkCoursePurchase,
    getAllCoursePurchases
} from '../controllers/coursePurchase.controller.js';
import { isLoggedIn, authorisedRoles } from '../middleware/auth.middleware.js';

const router = Router();

// User routes
router.route('/order')
    .post(isLoggedIn, createCourseOrder);

router.route('/verify')
    .post(isLoggedIn, verifyCoursePayment);

router.route('/my-courses')
    .get(isLoggedIn, getUserPurchasedCourses);

router.route('/check/:courseId')
    .get(isLoggedIn, checkCoursePurchase);

// Admin routes
router.route('/admin/all')
    .get(isLoggedIn, authorisedRoles('ADMIN'), getAllCoursePurchases);

export default router;
