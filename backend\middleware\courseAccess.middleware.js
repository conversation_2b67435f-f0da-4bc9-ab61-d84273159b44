import CoursePurchase from '../models/coursePurchase.model.js';
import AppError from '../utils/error.utils.js';

// Middleware to check if user has purchased a specific course
export const checkCourseAccess = async (req, res, next) => {
    try {
        const { role, id: userId } = req.user;
        const courseId = req.params.id || req.params.courseId || req.body.courseId;

        // Admin has access to all courses
        if (role === 'ADMIN') {
            return next();
        }

        if (!courseId) {
            return next(new AppError('Course ID is required', 400));
        }

        // Check if user has purchased this course
        const purchase = await CoursePurchase.findOne({ 
            userId, 
            courseId,
            paymentStatus: 'completed'
        });

        if (!purchase) {
            return next(new AppError('You must purchase this course to access its content', 403));
        }

        next();
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};

// Middleware to check if user can submit test (has purchased course and hasn't submitted yet)
export const checkTestAccess = async (req, res, next) => {
    try {
        const { role, id: userId } = req.user;
        const { courseId } = req.body;

        // Admin can access all tests
        if (role === 'ADMIN') {
            return next();
        }

        if (!courseId) {
            return next(new AppError('Course ID is required', 400));
        }

        // Check if user has purchased this course
        const purchase = await CoursePurchase.findOne({ 
            userId, 
            courseId,
            paymentStatus: 'completed'
        });

        if (!purchase) {
            return next(new AppError('You must purchase this course to access tests', 403));
        }

        next();
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};
