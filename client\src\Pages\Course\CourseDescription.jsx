import React, { useEffect, useState } from "react";
import Layout from "../../Layout/Layout";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { FaRupeeSign, FaPlay, FaLock } from "react-icons/fa";
import { createCourseOrder, verifyCoursePayment, checkCoursePurchase } from "../../Redux/Slices/CoursePurchaseSlice";
import { getRazorPayId } from "../../Redux/Slices/RazorpaySlice";
import toast from "react-hot-toast";

export default function CourseDescription() {
  const { state } = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { role, data, isLoggedIn } = useSelector((state) => state.auth);
  const { key } = useSelector((state) => state.razorpay);
  const { isLoading } = useSelector((state) => state.coursePurchase);

  const [hasPurchased, setHasPurchased] = useState(state?.hasPurchased || false);
  const [isCheckingPurchase, setIsCheckingPurchase] = useState(false);

  useEffect(() => {
    if(!state) {
      navigate("/courses");
    }
  }, []);

  useEffect(() => {
    if (isLoggedIn && state?._id) {
      checkPurchaseStatus();
    }
  }, [isLoggedIn, state?._id]);

  const checkPurchaseStatus = async () => {
    try {
      setIsCheckingPurchase(true);
      const result = await dispatch(checkCoursePurchase(state._id)).unwrap();
      setHasPurchased(result.hasPurchased);
    } catch (error) {
      console.error('Error checking purchase status:', error);
    } finally {
      setIsCheckingPurchase(false);
    }
  };

  const handlePurchaseCourse = async () => {
    if (!isLoggedIn) {
      toast.error('Please login to purchase the course');
      navigate('/login');
      return;
    }

    try {
      // Get Razorpay key if not available
      if (!key) {
        console.log('Fetching Razorpay key...');
        await dispatch(getRazorPayId());
      }

      // Create order
      console.log('Creating course order for course:', state._id);
      const orderResult = await dispatch(createCourseOrder(state._id)).unwrap();
      console.log('Order creation result:', orderResult);

      if (!orderResult.success) {
        toast.error('Failed to create order');
        return;
      }

      const razorpayKey = key || import.meta.env.VITE_REACT_APP_RAZORPAY_KEY_ID;

      if (!razorpayKey) {
        toast.error('Payment configuration error. Please contact administrator.');
        return;
      }

      const options = {
        key: razorpayKey,
        amount: orderResult.order.amount,
        currency: orderResult.order.currency,
        name: 'LMS Course Purchase',
        description: `Purchase ${orderResult.course.title}`,
        order_id: orderResult.order.id,
        handler: async function (response) {
          try {
            const paymentData = {
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              courseId: state._id
            };

            await dispatch(verifyCoursePayment(paymentData)).unwrap();
            setHasPurchased(true);
            toast.success('Course purchased successfully!');
          } catch (error) {
            toast.error('Payment verification failed');
          }
        },
        prefill: {
          name: data?.fullName,
          email: data?.email
        },
        theme: {
          color: '#3B82F6'
        }
      };

      const paymentObject = new window.Razorpay(options);
      paymentObject.on('payment.failed', function (response) {
        toast.error(response.error.description || 'Payment failed');
      });
      paymentObject.open();
    } catch (error) {
      console.error('Payment initiation error:', error);
      toast.error(error.message || 'Failed to initiate payment');
    }
  };

  const handleStartCourse = () => {
    if (hasPurchased || role === 'ADMIN') {
      navigate('/course/displaylectures', { state: state });
    } else {
      toast.error('Please purchase the course first');
    }
  };
  return (
    <Layout>
      <section className="min-h-[90vh] md:pt-12 pt-2 px-4 lg:px-20 flex flex-col   text-gray-800 dark:text-white">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 py-10 relative">
          <div className="lg:col-span-1 space-y-5">
            {/* Course Video or Thumbnail */}
            {state?.videoURL || state?.videoFile?.secure_url ? (
              <div className="space-y-3">
                {state?.videoURL ? (
                  <iframe
                    className="md:w-[87%] w-full h-64 rounded-md shadow-md"
                    src={state.videoURL}
                    title="Course Video"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  ></iframe>
                ) : (
                  <video
                    className="md:w-[87%] w-full h-64 rounded-md shadow-md"
                    src={state.videoFile.secure_url}
                    controls
                    controlsList="nodownload"
                  >
                    Your browser does not support the video tag.
                  </video>
                )}
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  📹 Course Preview Video
                </p>
              </div>
            ) : (
              <img
                className="md:w-[87%] w-full h-auto lg:h-64 rounded-md shadow-md"
                alt="thumbnail"
                src={state?.thumbnail?.secure_url}
              />
            )}

            <div className="space-y-4">
              <div className="flex flex-col text-lg font-inter">
                <p className="font-semibold">
                  <span className="text-yellow-600 dark:text-yellow-500 font-bold">
                    Total lectures:{" "}
                  </span>
                  {state?.numberOfLectures}
                </p>

                <p className="font-semibold">
                  <span className="text-yellow-600 dark:text-yellow-500 font-bold">
                    Instructor:{" "}
                  </span>
                  {state?.createdBy}
                </p>
              </div>
            </div>
          </div>

          <div className="lg:col-span-1 space-y-10 text-lg">
            <h1 className="md:text-3xl text-2xl lg:text-4xl font-bold font-lato text-yellow-500 mb-5 text-center w-fit after:content-[' '] relative after:absolute after:-bottom-3.5 after:left-0 after:h-1.5 after:w-[60%] after:rounded-full after:bg-purple-400 dark:after:bg-purple-600">
              {state?.title}
            </h1>

            <div className="space-y-1">
              <h2 className="text-2xl text-gray-800 dark:text-white font-[600] font-inter">
                Course description:
              </h2>
              <p className="text-lg text-gray-600 dark:text-violet-300 font-[500] font-nunito-sans whitespace-pre-wrap">
                {state?.description}
              </p>
            </div>

            {/* Price Display */}
            <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-lg font-semibold">Course Price:</span>
                <div className="flex items-center space-x-1">
                  <FaRupeeSign className="text-green-500" />
                  <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {state?.price || 0}
                  </span>
                </div>
              </div>
            </div>

            {/* Purchase Status */}
            {isLoggedIn && (
              <div className="text-center">
                {isCheckingPurchase ? (
                  <p className="text-gray-500">Checking purchase status...</p>
                ) : hasPurchased ? (
                  <p className="text-green-600 font-semibold flex items-center justify-center space-x-2">
                    <span>✓ You have purchased this course</span>
                  </p>
                ) : (
                  <p className="text-blue-600 font-semibold">Course available for purchase</p>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              {role === "ADMIN" || hasPurchased ? (
                <button
                  onClick={handleStartCourse}
                  className="bg-green-500 hover:bg-green-600 text-white text-xl rounded-md font-bold px-5 py-3 w-full transition-all ease-in-out duration-300 flex items-center justify-center space-x-2"
                >
                  <FaPlay className="text-sm" />
                  <span>Start Course</span>
                </button>
              ) : isLoggedIn ? (
                <button
                  onClick={handlePurchaseCourse}
                  disabled={isLoading}
                  className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white text-xl rounded-md font-bold px-5 py-3 w-full transition-all ease-in-out duration-300 flex items-center justify-center space-x-2"
                >
                  <FaRupeeSign className="text-sm" />
                  <span>{isLoading ? 'Processing...' : 'Buy Course'}</span>
                </button>
              ) : (
                <button
                  onClick={() => navigate('/login')}
                  className="bg-orange-500 hover:bg-orange-600 text-white text-xl rounded-md font-bold px-5 py-3 w-full transition-all ease-in-out duration-300 flex items-center justify-center space-x-2"
                >
                  <FaLock className="text-sm" />
                  <span>Login to Purchase</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
