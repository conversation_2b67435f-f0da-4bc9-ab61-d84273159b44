/**
 * Utility functions for YouTube URL validation and conversion
 */

/**
 * Validates if a URL is a valid YouTube embed URL
 * @param {string} url - The URL to validate
 * @returns {boolean} - True if valid YouTube embed URL
 */
export const isValidYouTubeEmbedURL = (url) => {
    if (!url) return false;
    const youtubeEmbedRegex = /^https:\/\/www\.youtube\.com\/embed\/[a-zA-Z0-9_-]+$/;
    return youtubeEmbedRegex.test(url);
};

/**
 * Converts a regular YouTube URL to embed format
 * @param {string} url - The YouTube URL to convert
 * @returns {string|null} - The embed URL or null if invalid
 */
export const convertToYouTubeEmbed = (url) => {
    if (!url) return null;
    
    // Already an embed URL
    if (isValidYouTubeEmbedURL(url)) {
        return url;
    }
    
    // Regular YouTube URL patterns
    const patterns = [
        /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/,
        /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]+)/,
        /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]+)/
    ];
    
    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match && match[1]) {
            return `https://www.youtube.com/embed/${match[1]}`;
        }
    }
    
    return null;
};

/**
 * Extracts video ID from YouTube URL
 * @param {string} url - The YouTube URL
 * @returns {string|null} - The video ID or null if invalid
 */
export const extractYouTubeVideoId = (url) => {
    if (!url) return null;
    
    const patterns = [
        /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/,
        /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]+)/,
        /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]+)/
    ];
    
    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match && match[1]) {
            return match[1];
        }
    }
    
    return null;
};
