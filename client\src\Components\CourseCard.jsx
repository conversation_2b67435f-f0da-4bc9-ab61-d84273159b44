import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { FaPlay, FaBook, FaUser, FaRupeeSign } from "react-icons/fa";
import { checkCoursePurchase } from "../Redux/Slices/CoursePurchaseSlice";

export default function CourseCard({ data }) {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isLoggedIn } = useSelector((state) => state.auth);
  const [hasPurchased, setHasPurchased] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isLoggedIn && data?._id) {
      checkPurchaseStatus();
    }
  }, [isLoggedIn, data?._id]);

  const checkPurchaseStatus = async () => {
    try {
      setIsLoading(true);
      const result = await dispatch(checkCoursePurchase(data._id)).unwrap();
      setHasPurchased(result.hasPurchased);
    } catch (error) {
      console.error('Error checking purchase status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCardClick = () => {
    navigate("/courses/description/", {state: {...data, hasPurchased}});
  };

  return (
    <div
      className="md:w-[22rem] w-[95%] md:h-[500px] h-[550px] shadow-custom dark:shadow-lg rounded-lg cursor-pointer group overflow-hidden bg-white dark:bg-zinc-700 transition-transform transform hover:scale-[1.01]"
      onClick={handleCardClick}
    >
      <div className="relative overflow-hidden">
        <img
          className="h-48 w-full rounded-tl-lg rounded-tr-lg group-hover:scale-[1.05] transition-all ease-in-out duration-300"
          src={data?.thumbnail?.secure_url}
          alt="course thumbnail"
        />
        <div className="absolute top-2 right-2 p-2 bg-white dark:bg-zinc-700 rounded-full">
          <FaPlay className="text-yellow-500 dark:text-yellow-400 text-xl" />
        </div>
      </div>
      <div className="p-4 md:space-y-2 space-y-3 text-gray-800 dark:text-white">
        <h2 className="text-2xl font-semibold line-clamp-2">{data?.title}</h2>
        <p className="line-clamp-2 font-nunito-sans text-base font-[500]">
          {data?.description}
        </p>
        <div className="flex items-center space-x-2">
          <FaBook className="text-yellow-500 dark:text-yellow-400" />
          <p className="text-base font-semibold">Category: {data?.category}</p>
        </div>
        <div className="flex items-center space-x-2">
          <FaBook className="text-yellow-500 dark:text-yellow-400" />
          <p className="text-base font-semibold">
            Total lectures: {data?.numberoflectures}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <FaUser className="text-yellow-500 dark:text-yellow-400" />
          <p className="text-base font-semibold">
            Instructor: {data?.createdBy}
          </p>
        </div>
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center space-x-1">
            <FaRupeeSign className="text-green-500 dark:text-green-400" />
            <p className="text-lg font-bold text-green-600 dark:text-green-400">
              {data?.price || 0}
            </p>
          </div>
          {isLoggedIn && (
            <div className="text-sm">
              {isLoading ? (
                <span className="text-gray-500">Checking...</span>
              ) : hasPurchased ? (
                <span className="text-green-600 font-semibold">✓ Purchased</span>
              ) : (
                <span className="text-blue-600 font-semibold">Available</span>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
